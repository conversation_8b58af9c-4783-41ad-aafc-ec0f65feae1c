import { Button, InputNumber } from 'antd'

import { Minus, Plus } from '@/components'

interface QuantitySelectorProps {
  quantity: number
  onQuantityChange: (quantity: number, oldValue: number) => void
  maxQuantity: number
  minQuantity?: number
  small?: boolean
  isIncrementDisabled?: boolean
  /**
   * 忽略最大值限制，允许点击加号但由父组件处理限制逻辑
   * 如果为 true，点击加号按钮时不会在组件内部阻止超过 maxQuantity
   * 默认为 false，保持向后兼容的行为
   */
  ignoreMaxLimit?: boolean
}

const QuantityButton = ({
  quantity,
  onQuantityChange,
  maxQuantity = 99,
  minQuantity = 1,
  small = false,
  isIncrementDisabled = false,
  ignoreMaxLimit = false,
}: QuantitySelectorProps) => {
  const handleQuantityChange = (step: number) => {
    const newValue = quantity + step

    if (step < 0) {
      // 减少数量时的逻辑
      if (quantity > maxQuantity) {
        // 如果当前数量超过最大值，点击减号直接设置为最大值
        onQuantityChange(maxQuantity, quantity)
      } else if (newValue >= minQuantity) {
        // 正常减少逻辑
        onQuantityChange(newValue, quantity)
      }
    } else {
      // 增加数量时，根据 ignoreMaxLimit 决定行为
      if (ignoreMaxLimit) {
        // 忽略最大值限制，直接调用 onQuantityChange
        onQuantityChange(newValue, quantity)
      } else {
        // 保持原有行为，仅在不超过最大值时调用
        if (newValue <= maxQuantity) {
          onQuantityChange(newValue, quantity)
        }
      }
    }
  }

  const inputQuantityChange = (value: number) => {
    const newValue = value
    if (newValue >= maxQuantity) {
      onQuantityChange(maxQuantity, quantity)
    } else if (newValue <= minQuantity) {
      onQuantityChange(minQuantity, quantity)
    } else {
      onQuantityChange(newValue, quantity)
    }
  }

  return (
    <div className={`number-up-down-field ${small ? 'small' : ''}`}>
      <Button
        className="change-btn"
        icon={<Minus size={small ? 14 : 16} />}
        type="text"
        disabled={quantity <= minQuantity || isIncrementDisabled}
        onClick={() => handleQuantityChange(-1)}
      />
      <InputNumber
        min={minQuantity}
        max={maxQuantity}
        value={quantity}
        onChange={(value) => inputQuantityChange(value ?? minQuantity)}
        controls={false}
        variant="borderless"
        className="quantity-input"
        disabled={isIncrementDisabled}
      />
      <Button
        className="change-btn"
        icon={<Plus size={small ? 14 : 16} />}
        type="text"
        disabled={(ignoreMaxLimit ? false : quantity >= maxQuantity) || isIncrementDisabled}
        onClick={() => handleQuantityChange(1)}
      />
    </div>
  )
}

export default QuantityButton
